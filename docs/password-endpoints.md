# Password Management Endpoints

This document describes the newly implemented password management endpoints for the CSP API.

## Endpoints

### 1. Change Password (Self-Service)

**Endpoint:** `POST /me/change-password`

**Description:** Allows authenticated users to change their own password by providing their current password and a new password.

**Authentication:** Required (Bearer token)

**Request Body:**
```json
{
  "current_password": "string",
  "new_password": "string"
}
```

**Response:**
- **200 OK:** Password changed successfully
  ```json
  {
    "message": "Password changed successfully"
  }
  ```

- **400 Bad Request:** Invalid current password
  ```json
  {
    "code": "INVALID_CURRENT_PASSWORD",
    "message": "Current password is incorrect"
  }
  ```

- **401 Unauthorized:** Invalid or missing authentication token

**Validation:**
- `current_password`: Required
- `new_password`: Required

### 2. Reset User Password (Admin)

**Endpoint:** `POST /users/:id/reset-password`

**Description:** Allows administrators to reset any user's password without requiring the current password. This also sets the `is_required_reset_password` flag to `false`.

**Authentication:** Required (Bearer token)

**Path Parameters:**
- `id`: User ID (UUID)

**Request Body:**
```json
{
  "new_password": "string"
}
```

**Response:**
- **200 OK:** Password reset successfully
  ```json
  {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "display_name": "User Name",
    "type": "USER",
    "is_required_reset_password": false,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
  ```

- **404 Not Found:** User not found
  ```json
  {
    "code": "USER_NOT_FOUND",
    "message": "User not found"
  }
  ```

- **401 Unauthorized:** Invalid or missing authentication token

**Validation:**
- `new_password`: Required

## Implementation Details

### Security Features

1. **Password Hashing:** All passwords are hashed using bcrypt before storage
2. **Current Password Verification:** The change password endpoint verifies the current password before allowing changes
3. **Authentication Required:** Both endpoints require valid authentication tokens
4. **Password Reset Flag:** The reset password endpoint automatically sets `is_required_reset_password` to `false`

### Database Changes

The implementation uses the existing `is_required_reset_password` field in the users table:
- When a user's password is reset by an admin, this flag is set to `false`
- This can be used to track whether a user needs to change their password on next login

### Error Handling

Both endpoints include comprehensive error handling for:
- Invalid authentication tokens
- User not found scenarios
- Password hashing failures
- Database operation failures
- Invalid current password (for change password endpoint)

### Testing

A Postman collection has been created at `postman-collections/password-endpoints.json` with:
- Example requests for both endpoints
- Test scripts to validate responses
- Sample success and error responses

## Usage Examples

### Change Own Password
```bash
curl -X POST http://localhost:8080/me/change-password \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "current_password": "oldpassword123",
    "new_password": "newpassword123"
  }'
```

### Reset User Password (Admin)
```bash
curl -X POST http://localhost:8080/users/USER_ID/reset-password \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "new_password": "resetpassword123"
  }'
```

## Files Modified/Created

### New Files:
- `docs/password-endpoints.md` - This documentation
- `postman-collections/password-endpoints.json` - Postman collection for testing

### Modified Files:
- `requests/user_create.request.go` - Added ChangePassword and ResetPassword request structures
- `services/user.dto.go` - Added ResetPasswordPayload
- `services/user.service.go` - Added ResetPassword method and interface
- `services/me.service.go` - Added ChangePassword method and ChangePasswordPayload
- `modules/me/me.controller.go` - Added ChangePassword controller method
- `modules/me/me.http.go` - Added change password route
- `modules/user/user.controller.go` - Added ResetPassword controller method
- `modules/user/user.http.go` - Added reset password route
