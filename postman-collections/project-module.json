{"info": {"_postman_id": "project-module-collection", "name": "CSP API - Project Module", "description": "Project CRUD endpoints for CSP API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:3001", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "project_id", "value": "", "type": "string"}, {"key": "ministry_id", "value": "", "type": "string"}, {"key": "department_id", "value": "", "type": "string"}, {"key": "division_id", "value": "", "type": "string"}], "item": [{"name": "Create Project", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('project_id', response.id);", "    console.log('Project ID saved:', response.id);", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Digital Transformation Project\",\n  \"contact_name\": \"<PERSON>\",\n  \"contact_phone\": \"+66-2-123-4567\",\n  \"contact_email\": \"<EMAIL>\",\n  \"budget\": 5000000.50,\n  \"ministry_id\": \"{{ministry_id}}\",\n  \"department_id\": \"{{department_id}}\",\n  \"division_id\": \"{{division_id}}\"\n}"}, "url": {"raw": "{{base_url}}/projects", "host": ["{{base_url}}"], "path": ["projects"]}, "description": "Create a new project with all details including organizational hierarchy."}, "response": [{"name": "Successful Creation", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Digital Transformation Project\",\n  \"contact_name\": \"<PERSON>\",\n  \"contact_phone\": \"+66-2-123-4567\",\n  \"contact_email\": \"<EMAIL>\",\n  \"budget\": 5000000.50,\n  \"ministry_id\": \"ministry-uuid-here\",\n  \"department_id\": \"department-uuid-here\",\n  \"division_id\": \"division-uuid-here\"\n}"}, "url": {"raw": "{{base_url}}/projects", "host": ["{{base_url}}"], "path": ["projects"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"project-uuid-here\",\n  \"name\": \"Digital Transformation Project\",\n  \"contact_name\": \"<PERSON>\",\n  \"contact_phone\": \"+66-2-123-4567\",\n  \"contact_email\": \"<EMAIL>\",\n  \"budget\": 5000000.50,\n  \"ministry_id\": \"ministry-uuid-here\",\n  \"department_id\": \"department-uuid-here\",\n  \"division_id\": \"division-uuid-here\",\n  \"created_by_id\": \"user-uuid-here\",\n  \"updated_by_id\": null,\n  \"deleted_by_id\": null,\n  \"created_at\": \"2024-01-01T00:00:00Z\",\n  \"updated_at\": \"2024-01-01T00:00:00Z\",\n  \"ministry\": {\n    \"id\": \"ministry-uuid-here\",\n    \"name_th\": \"กระทรวงดิจิทัลเพื่อเศรษฐกิจและสังคม\",\n    \"name_en\": \"Ministry of Digital Economy and Society\"\n  },\n  \"department\": {\n    \"id\": \"department-uuid-here\",\n    \"name_th\": \"กรมพัฒนาดิจิทัล\",\n    \"name_en\": \"Department of Digital Development\"\n  },\n  \"division\": {\n    \"id\": \"division-uuid-here\",\n    \"name_th\": \"ฝ่ายเทคโนโลยี\",\n    \"name_en\": \"Technology Division\"\n  },\n  \"created_by\": {\n    \"id\": \"user-uuid-here\",\n    \"email\": \"<EMAIL>\",\n    \"display_name\": \"Admin User\"\n  }\n}"}]}, {"name": "Get All Projects", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/projects?page=1&limit=10", "host": ["{{base_url}}"], "path": ["projects"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get paginated list of all projects with their relationships."}, "response": []}, {"name": "Get Projects by Ministry", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/projects?ministry_id={{ministry_id}}&page=1&limit=10", "host": ["{{base_url}}"], "path": ["projects"], "query": [{"key": "ministry_id", "value": "{{ministry_id}}"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get projects filtered by ministry ID."}, "response": []}, {"name": "Get Projects by Department", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/projects?department_id={{department_id}}&page=1&limit=10", "host": ["{{base_url}}"], "path": ["projects"], "query": [{"key": "department_id", "value": "{{department_id}}"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get projects filtered by department ID."}, "response": []}, {"name": "Get Projects by Division", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/projects?division_id={{division_id}}&page=1&limit=10", "host": ["{{base_url}}"], "path": ["projects"], "query": [{"key": "division_id", "value": "{{division_id}}"}, {"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get projects filtered by division ID."}, "response": []}, {"name": "Get Project by ID", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/projects/{{project_id}}", "host": ["{{base_url}}"], "path": ["projects", "{{project_id}}"]}, "description": "Get a specific project by its ID with all relationships."}, "response": [{"name": "Successful Retrieval", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/projects/{{project_id}}", "host": ["{{base_url}}"], "path": ["projects", "{{project_id}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"project-uuid-here\",\n  \"name\": \"Digital Transformation Project\",\n  \"contact_name\": \"<PERSON>\",\n  \"contact_phone\": \"+66-2-123-4567\",\n  \"contact_email\": \"<EMAIL>\",\n  \"budget\": 5000000.50,\n  \"ministry_id\": \"ministry-uuid-here\",\n  \"department_id\": \"department-uuid-here\",\n  \"division_id\": \"division-uuid-here\",\n  \"created_by_id\": \"user-uuid-here\",\n  \"updated_by_id\": null,\n  \"deleted_by_id\": null,\n  \"created_at\": \"2024-01-01T00:00:00Z\",\n  \"updated_at\": \"2024-01-01T00:00:00Z\",\n  \"ministry\": {\n    \"id\": \"ministry-uuid-here\",\n    \"name_th\": \"กระทรวงดิจิทัลเพื่อเศรษฐกิจและสังคม\",\n    \"name_en\": \"Ministry of Digital Economy and Society\"\n  },\n  \"department\": {\n    \"id\": \"department-uuid-here\",\n    \"name_th\": \"กรมพัฒนาดิจิทัล\",\n    \"name_en\": \"Department of Digital Development\"\n  },\n  \"division\": {\n    \"id\": \"division-uuid-here\",\n    \"name_th\": \"ฝ่ายเทคโนโลยี\",\n    \"name_en\": \"Technology Division\"\n  },\n  \"created_by\": {\n    \"id\": \"user-uuid-here\",\n    \"email\": \"<EMAIL>\",\n    \"display_name\": \"Admin User\"\n  }\n}"}, {"name": "Project Not Found", "originalRequest": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/projects/invalid-uuid", "host": ["{{base_url}}"], "path": ["projects", "invalid-uuid"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": \"NOT_FOUND\",\n  \"message\": \"Project not found\"\n}"}]}, {"name": "Update Project", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Digital Transformation Project\",\n  \"contact_name\": \"<PERSON>\",\n  \"contact_phone\": \"+66-2-987-6543\",\n  \"contact_email\": \"<EMAIL>\",\n  \"budget\": 7500000.75\n}"}, "url": {"raw": "{{base_url}}/projects/{{project_id}}", "host": ["{{base_url}}"], "path": ["projects", "{{project_id}}"]}, "description": "Update an existing project. All fields are optional."}, "response": [{"name": "Successful Update", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Digital Transformation Project\",\n  \"contact_name\": \"<PERSON>\",\n  \"contact_phone\": \"+66-2-987-6543\",\n  \"contact_email\": \"<EMAIL>\",\n  \"budget\": 7500000.75\n}"}, "url": {"raw": "{{base_url}}/projects/{{project_id}}", "host": ["{{base_url}}"], "path": ["projects", "{{project_id}}"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"project-uuid-here\",\n  \"name\": \"Updated Digital Transformation Project\",\n  \"contact_name\": \"<PERSON>\",\n  \"contact_phone\": \"+66-2-987-6543\",\n  \"contact_email\": \"<EMAIL>\",\n  \"budget\": 7500000.75,\n  \"ministry_id\": \"ministry-uuid-here\",\n  \"department_id\": \"department-uuid-here\",\n  \"division_id\": \"division-uuid-here\",\n  \"created_by_id\": \"user-uuid-here\",\n  \"updated_by_id\": \"user-uuid-here\",\n  \"deleted_by_id\": null,\n  \"created_at\": \"2024-01-01T00:00:00Z\",\n  \"updated_at\": \"2024-01-01T12:00:00Z\",\n  \"ministry\": {\n    \"id\": \"ministry-uuid-here\",\n    \"name_th\": \"กระทรวงดิจิทัลเพื่อเศรษฐกิจและสังคม\",\n    \"name_en\": \"Ministry of Digital Economy and Society\"\n  },\n  \"department\": {\n    \"id\": \"department-uuid-here\",\n    \"name_th\": \"กรมพัฒนาดิจิทัล\",\n    \"name_en\": \"Department of Digital Development\"\n  },\n  \"division\": {\n    \"id\": \"division-uuid-here\",\n    \"name_th\": \"ฝ่ายเทคโนโลยี\",\n    \"name_en\": \"Technology Division\"\n  },\n  \"created_by\": {\n    \"id\": \"user-uuid-here\",\n    \"email\": \"<EMAIL>\",\n    \"display_name\": \"Admin User\"\n  },\n  \"updated_by\": {\n    \"id\": \"user-uuid-here\",\n    \"email\": \"<EMAIL>\",\n    \"display_name\": \"Admin User\"\n  }\n}"}]}, {"name": "Delete Project", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/projects/{{project_id}}", "host": ["{{base_url}}"], "path": ["projects", "{{project_id}}"]}, "description": "Soft delete a project. The project will be marked as deleted but not physically removed from the database."}, "response": [{"name": "Successful Deletion", "originalRequest": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/projects/{{project_id}}", "host": ["{{base_url}}"], "path": ["projects", "{{project_id}}"]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": null, "header": [], "cookie": [], "body": null}, {"name": "Project Not Found", "originalRequest": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/projects/invalid-uuid", "host": ["{{base_url}}"], "path": ["projects", "invalid-uuid"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"code\": \"NOT_FOUND\",\n  \"message\": \"Project not found\"\n}"}]}]}