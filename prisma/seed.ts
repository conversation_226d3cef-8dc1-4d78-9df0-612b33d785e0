import { PrismaClient } from "@prisma/client";
import * as bcrypt from "bcryptjs";

const prisma = new PrismaClient();

async function main() {
  // Hash the password using bcrypt (same as <PERSON>'s bcrypt.DefaultCost which is 10)
  const hashedPassword = await bcrypt.hash("12345678", 10);

  // Check if admin user already exists
  const existingAdmin = await prisma.users.findUnique({
    where: { email: "<EMAIL>" }
  });

  if (existingAdmin) {
    console.log("Admin user already exists, skipping creation");
    return;
  }

  // Create admin user
  const adminUser = await prisma.users.create({
    data: {
      email: "<EMAIL>",
      password: hashedPassword,
      display_name: "admin",
      type: "ADMIN",
    }
  });

  console.log("Admin user created:", {
    id: adminUser.id,
    email: adminUser.email,
    display_name: adminUser.display_name
  });
}
main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
