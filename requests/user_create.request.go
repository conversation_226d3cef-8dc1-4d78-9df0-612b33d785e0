package requests

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type UserCreate struct {
	core.BaseValidator
	Email       *string `json:"email"`
	Password    *string `json:"password"`
	DisplayName *string `json:"display_name"`
}

func (r *UserCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsEmail(r.Email, "email"))
	r.Must(r.IsStrRequired(r.Email, "email"))
	r.Must(r.IsStrUnique(ctx, r.Email, models.User{}.TableName(), "email", "", "email"))

	r.Must(r.Is<PERSON>tr<PERSON>equired(r.Password, "password"))

	return r.Error()
}

type Login struct {
	core.BaseValidator
	Email    *string `json:"email"`
	Password *string `json:"password"`
}

func (r *Login) Valid(ctx core.IContext) core.IError {
	r.Must(r.<PERSON>mail(r.<PERSON>ail, "email"))
	r.Must(r.Is<PERSON>trRequired(r.<PERSON>ail, "email"))
	r.Must(r.Is<PERSON>trRequired(r.Password, "password"))

	return r.Error()
}

type ChangePassword struct {
	core.BaseValidator
	CurrentPassword *string `json:"current_password"`
	NewPassword     *string `json:"new_password"`
}

func (r *ChangePassword) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.CurrentPassword, "current_password"))
	r.Must(r.IsStrRequired(r.NewPassword, "new_password"))

	return r.Error()
}

type ResetPassword struct {
	core.BaseValidator
	NewPassword *string `json:"new_password"`
}

func (r *ResetPassword) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.NewPassword, "new_password"))

	return r.Error()
}
