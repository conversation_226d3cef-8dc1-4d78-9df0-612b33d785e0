### Variables
@base_url = http://localhost:3001
@auth_token = your-auth-token-here
@project_id = project-uuid-here
@ministry_id = ministry-uuid-here
@department_id = department-uuid-here
@division_id = division-uuid-here

### Login (to get auth token)
POST {{base_url}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

### Create Project
POST {{base_url}}/projects
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "name": "Digital Transformation Project",
  "contact_name": "<PERSON>",
  "contact_phone": "+66-2-123-4567",
  "contact_email": "<EMAIL>",
  "budget": 5000000.50,
  "ministry_id": "{{ministry_id}}",
  "department_id": "{{department_id}}",
  "division_id": "{{division_id}}"
}

### Create Project (Minimal)
POST {{base_url}}/projects
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "name": "Simple Project"
}

### Get All Projects
GET {{base_url}}/projects?page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Projects by Ministry
GET {{base_url}}/projects?ministry_id={{ministry_id}}&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Projects by Department
GET {{base_url}}/projects?department_id={{department_id}}&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Projects by Division
GET {{base_url}}/projects?division_id={{division_id}}&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Projects by Created By
GET {{base_url}}/projects?created_by_id=user-uuid-here&page=1&limit=10
Authorization: Bearer {{auth_token}}

### Get Project by ID
GET {{base_url}}/projects/{{project_id}}
Authorization: Bearer {{auth_token}}

### Update Project (Full)
PUT {{base_url}}/projects/{{project_id}}
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "name": "Updated Digital Transformation Project",
  "contact_name": "Jane Smith",
  "contact_phone": "+66-2-987-6543",
  "contact_email": "<EMAIL>",
  "budget": 7500000.75,
  "ministry_id": "{{ministry_id}}",
  "department_id": "{{department_id}}",
  "division_id": "{{division_id}}"
}

### Update Project (Partial)
PUT {{base_url}}/projects/{{project_id}}
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "name": "Partially Updated Project",
  "budget": 3000000.00
}

### Delete Project
DELETE {{base_url}}/projects/{{project_id}}
Authorization: Bearer {{auth_token}}

### Test Validation - Invalid Email
POST {{base_url}}/projects
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "name": "Test Project",
  "contact_email": "invalid-email"
}

### Test Validation - Missing Required Name
POST {{base_url}}/projects
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "contact_name": "John Doe"
}

### Test Validation - Invalid Ministry ID
POST {{base_url}}/projects
Content-Type: application/json
Authorization: Bearer {{auth_token}}

{
  "name": "Test Project",
  "ministry_id": "invalid-uuid"
}
